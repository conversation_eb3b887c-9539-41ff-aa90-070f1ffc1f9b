const {
  default: makeWASocket,
  useMultiFileAuthState,
  fetchLatestBaileysVersion,
  prepareWAMessageMedia,
  downloadMediaMessage, // para download de mídia
} = require("@whiskeysockets/baileys");

const pino = require("pino");
const chalk = require("chalk").default;
const qrcode = require("qrcode-terminal");
const { exec } = require("child_process");
const fs = require("fs");
const gerarMenu = require("./menu");
const yts = require("yt-search");
const path = require("path");
const readline = require("readline");

// --- Funções de Levenshtein ---
function levenshtein(a, b) {
  const matrix = Array.from({ length: b.length + 1 }, (_, i) => [i]);
  for (let j = 0; j <= a.length; j++) matrix[0][j] = j;
  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      if (b[i - 1] === a[j - 1]) matrix[i][j] = matrix[i - 1][j - 1];
      else matrix[i][j] = Math.min(matrix[i - 1][j - 1], matrix[i][j - 1], matrix[i - 1][j]) + 1;
    }
  }
  return matrix[b.length][a.length];
}

function sugerirComando(text, comandos) {
  let menorDist = Infinity;
  let sugestao = null;
  for (const cmd of comandos) {
    const dist = levenshtein(text, cmd);
    if (dist < menorDist) {
      menorDist = dist;
      sugestao = cmd;
    }
  }
  return menorDist <= 3 ? sugestao : null;
}

// --- Funções de Donos ---
function carregarDonos() {
  if (!fs.existsSync("./donos.json")) {
    fs.writeFileSync("./donos.json", JSON.stringify({ dono: "", subdonos: Array(10).fill("") }, null, 2));
  }
  return JSON.parse(fs.readFileSync("./donos.json"));
}

function salvarDonos(dados) {
  fs.writeFileSync("./donos.json", JSON.stringify(dados, null, 2));
}

// --- Configurações ---
function carregarConfig() {
  if (!fs.existsSync("./config.json")) {
    fs.writeFileSync("./config.json", JSON.stringify({ prefix: "®" }, null, 2));
  }
  return JSON.parse(fs.readFileSync("./config.json"));
}

function salvarConfig(config) {
  fs.writeFileSync("./config.json", JSON.stringify(config, null, 2));
}

// --- Reiniciar bot ---
function reiniciarBot() {
  console.log(chalk.yellow("🔄 Arquivo alterado, reiniciando..."));
  exec("node .", (err) => { if(err) console.error(err); });
  process.exit(0);
}

// --- Auto-restart removido para evitar desconexões desnecessárias ---
// fs.watchFile(__filename, () => reiniciarBot());

// --- Função para mostrar menu de conexão ---
function showConnectionMenu() {
  return new Promise((resolve) => {
    console.clear();
    console.log(chalk.magenta.bold("╔══════════════════════════════════════╗"));
    console.log(chalk.magenta.bold("║        🌸 HELLOKITTY BOT 🌸          ║"));
    console.log(chalk.magenta.bold("║           DUAL CONNECT               ║"));
    console.log(chalk.magenta.bold("╠══════════════════════════════════════╣"));
    console.log(chalk.cyan("║  Escolha o método de conexão:        ║"));
    console.log(chalk.yellow("║                                      ║"));
    console.log(chalk.green("║  [1] 📱 Conexão via Código           ║"));
    console.log(chalk.blue("║  [2] 📲 Conexão via QR Code          ║"));
    console.log(chalk.yellow("║                                      ║"));
    console.log(chalk.magenta.bold("╚══════════════════════════════════════╝"));
    console.log();

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question(chalk.white.bold("💖 Digite sua opção (1 ou 2): "), (answer) => {
      rl.close();
      const option = answer.trim();
      if (option === "1" || option === "2") {
        resolve(option);
      } else {
        console.log(chalk.red("❌ Opção inválida! Tente novamente..."));
        setTimeout(() => {
          showConnectionMenu().then(resolve);
        }, 2000);
      }
    });
  });
}

// --- Função para conectar via código ---
async function connectViaCode() {
  const { state, saveCreds } = await useMultiFileAuthState("./auth");
  const { version } = await fetchLatestBaileysVersion();

  const hello = makeWASocket({
    version,
    auth: state,
    logger: pino({ level: "silent" }),
    printQRInTerminal: false
  });

  if (!hello.authState.creds.registered) {
    console.clear();
    console.log(chalk.yellow("🔐 Gerando código de pareamento..."));

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const phoneNumber = await new Promise((resolve) => {
      rl.question(chalk.cyan("📱 Digite seu número (com código do país, ex: 5511999999999): "), (number) => {
        rl.close();
        resolve(number.replace(/\D/g, ""));
      });
    });

    try {
      const code = await hello.requestPairingCode(phoneNumber);
      console.log(chalk.green.bold(`\n🌸 Seu código de pareamento: ${code}`));
      console.log(chalk.yellow("📱 Abra o WhatsApp > Dispositivos Conectados > Conectar Dispositivo"));
      console.log(chalk.yellow("📝 Digite o código acima no seu WhatsApp"));
      console.log(chalk.cyan("⏳ Aguardando conexão..."));
    } catch (error) {
      console.log(chalk.red("❌ Erro ao gerar código:", error.message));
      return connectViaCode();
    }
  }

  return { hello, saveCreds };
}

// --- Função para conectar via QR ---
async function connectViaQR() {
  const { state, saveCreds } = await useMultiFileAuthState("./auth");
  const { version } = await fetchLatestBaileysVersion();

  const hello = makeWASocket({
    version,
    auth: state,
    logger: pino({ level: "silent" }),
    printQRInTerminal: true
  });

  return { hello, saveCreds };
}

async function startBot() {
  let config = carregarConfig();
  let prefix = config.prefix;
  let hello, saveCreds;

  // Verifica se já existe autenticação
  const authExists = fs.existsSync("./auth") && fs.readdirSync("./auth").length > 0;

  if (!authExists) {
    // Primeira conexão - mostra menu
    const connectionMethod = await showConnectionMenu();

    if (connectionMethod === "1") {
      console.log(chalk.magenta("🔐 Conectando via código..."));
      ({ hello, saveCreds } = await connectViaCode());
    } else {
      console.log(chalk.blue("📲 Conectando via QR Code..."));
      ({ hello, saveCreds } = await connectViaQR());
    }
  } else {
    // Reconexão automática - tenta sem QR primeiro
    console.log(chalk.green("🔄 Reconectando automaticamente..."));
    const { state, saveCreds: saveCreds_temp } = await useMultiFileAuthState("./auth");
    const { version } = await fetchLatestBaileysVersion();

    hello = makeWASocket({
      version,
      auth: state,
      logger: pino({ level: "silent" }),
      printQRInTerminal: false // nunca mostra QR na reconexão automática
    });

    saveCreds = saveCreds_temp;
  }

  // Conexão
  hello.ev.on("connection.update", ({ connection, qr, lastDisconnect }) => {
    if (qr && !hello.authState.creds.registered) {
      // Só mostra QR se realmente não estiver registrado
      console.clear();
      console.log(chalk.yellow("📲 Escaneie este QR Code no seu WhatsApp:"));
      qrcode.generate(qr, { small: true });
    }
    if (connection === "open") {
      console.clear();
      console.log(chalk.green.bold("✅ Bot conectado com sucesso!"));
      console.log(chalk.cyan("🌸 HELLOKITTY BOT está online! 🌸"));
    }
    if (connection === "close") {
      console.log(chalk.red("❌ Conexão encerrada, tentando reconectar..."));
      // Aguarda um pouco mais antes de reconectar para evitar loops
      setTimeout(() => startBot(), 5000);
    }
  });

  hello.ev.on("creds.update", saveCreds);

  const comandos = [
    "ping", "reiniciar", "info", "menu",
    "subdono1","subdono2","subdono3","subdono4","subdono5",
    "subdono6","subdono7","subdono8","subdono9","subdono10",
    "subdonos", "prefixo", "play", "playvid", "8d", "8dvideo", "ban", "promover", "rebaixar", "mute", "desmute", "d", "reviver", "infogp"
  ];

  // --- Função para checar admins ---
  async function checkGroupPermissions(chatId, userId) {
    const groupMetadata = await hello.groupMetadata(chatId);
    const admins = groupMetadata.participants.filter(p => p.admin).map(p => p.id);
    const isUserAdmin = admins.includes(userId);
    const botJid = hello.user.id.split(":")[0] + "@s.whatsapp.net";
    const isBotAdmin = admins.includes(botJid);
    return { isGroup: chatId.endsWith("@g.us"), isUserAdmin, isBotAdmin, admins };
  }
  
  // Receber mensagens //BY : ALVES
 hello.ev.on("messages.upsert", async ({ messages }) => {
  const m = messages[0];
  if (!m.message) return;

  const isGroup = m.key.remoteJid.endsWith("@g.us");
  let chatName = "Privado";
  let senderName = m.pushName || "Desconhecido";
  const numeroSender = m.key.participant
    ? m.key.participant.split("@")[0]
    : m.key.remoteJid.split("@")[0];

  if (isGroup) {
    try {
      const metadata = await hello.groupMetadata(m.key.remoteJid);
      chatName = metadata.subject || "Grupo sem nome";
    } catch {
      chatName = "Grupo";
    }
  }

  let tipoConteudo = "";
  if (m.message.imageMessage) tipoConteudo = "imagem 📷";
  else if (m.message.videoMessage) tipoConteudo = "vídeo 🎥";
  else if (m.message.audioMessage) tipoConteudo = "áudio 🎵";
  else if (m.message.stickerMessage) tipoConteudo = "sticker 🏷️";
  else if (m.message.documentMessage?.mimetype?.includes("gif")) tipoConteudo = "gif 🎞️";
  else if (m.message.documentMessage) tipoConteudo = "documento 📄";
  else if (m.message.contactMessage) tipoConteudo = "contato 👤";
  else if (m.message.locationMessage) tipoConteudo = "localização 📍";

  let textContent = m.message.conversation || m.message.extendedTextMessage?.text;
  if (!textContent) {
    if (m.message.imageMessage?.caption) textContent = m.message.imageMessage.caption;
    else if (m.message.videoMessage?.caption) textContent = m.message.videoMessage.caption;
    else if (m.message.documentMessage?.caption) textContent = m.message.documentMessage.caption;
    else textContent = tipoConteudo || "[Sem conteúdo]";
  }

  const text = textContent;
  const respondeu = m.message.extendedTextMessage?.contextInfo?.quotedMessage ? "Sim" : "Não";
  const hora = new Date().toLocaleString("pt-BR", { hour12: false });

  // Letras mais fortes com underline
  let tipoMsg;
  if (text.startsWith(prefix)) tipoMsg = chalk.black.bold.underline.bgYellow(" [COMANDO] ");
  else if (m.key.fromMe) tipoMsg = chalk.white.bold.underline.bgGreen(" [ENVIADA] ");
  else tipoMsg = chalk.white.bold.underline.bgCyan(" [MENSAGEM] ");

  const usuario = chalk.magenta(`Usuário: ${senderName} (${numeroSender})`);
  const grupoOuPrivado = isGroup ? chalk.blue(`Grupo: ${chatName}`) : chalk.green(`Privado: ${numeroSender}`);
  const conteudo = chalk.white(
    `${tipoConteudo ? tipoConteudo + " | " : ""}Respondeu outra mensagem? ${respondeu} | ${text}`
  );

  console.log(`${tipoMsg} ${hora} | ${usuario} | ${grupoOuPrivado} | ${conteudo}`);
//----------------

    const dadosDonos = carregarDonos();
    const dono = dadosDonos.dono;
    const subdonos = dadosDonos.subdonos || [];
    const ehDonoOuSub = numeroSender === dono || subdonos.includes(numeroSender);

    if (!text.startsWith(prefix)) return;
    const args = text.slice(prefix.length).trim().split(/ +/);
    const command = args.shift().toLowerCase();

    // --- Comandos ---
if (command === "ping") {
    try {
        // Salva o horário de inicialização do bot (se ainda não existir)
        const botStartTime = global.botStartTime || (global.botStartTime = Date.now());

        const formatUptime = (ms) => {
            let seconds = Math.floor(ms / 1000);
            let minutes = Math.floor(seconds / 60);
            let hours = Math.floor(minutes / 60);
            seconds %= 60;
            minutes %= 60;
            return `${hours}h ${minutes}m ${seconds}s`;
        };

        // Calcula a latência real do bot baseada na mensagem
        const getBotLatency = () => {
            const messageTime = m.messageTimestamp * 1000; // converte para ms
            const currentTime = Date.now();
            const latency = currentTime - messageTime;
            return Math.max(latency, 1); // evita valores negativos
        };

        // Mede o tempo de processamento e responde direto
        const commandStartTime = Date.now();
        const botLatency = getBotLatency();
        const processingTime = Date.now() - commandStartTime;

        let indicator = botLatency <= 300 ? "🔵" : botLatency <= 570 ? "🟢" : botLatency <= 800 ? "🟡" : botLatency <= 1200 ? "🟠" : "🔴";
        let speedText = botLatency <= 300 ? "ultra rápido" : botLatency <= 570 ? "rápido" : botLatency <= 800 ? "estável" : botLatency <= 1200 ? "lento" : "super lento";

        const responseText = `🏓 *Pong!*\n${indicator} ${botLatency}ms (${speedText})\n\n` +
                           `⚡ *Processamento:* ${processingTime}ms\n` +
                           `🕒 *Uptime:* ${formatUptime(Date.now() - botStartTime)}\n` +
                           `💻 *Bot:* HELLOKITTY BOT\n` +
                           `📅 *${new Date().toLocaleTimeString('pt-BR')}*`;

        await hello.sendMessage(m.key.remoteJid, { text: responseText });

    } catch (err) {
        console.error("[PING-ERRO]", err);
        await hello.sendMessage(m.key.remoteJid, { text: "❌ Erro ao calcular ping." });
    }
 return; // add
} 

       if (command === "reiniciar") {
      if (!ehDonoOuSub) return await hello.sendMessage(m.key.remoteJid, { text: "❌ Apenas a dona ou os subdonos podem reiniciar o bot." });
      await hello.sendMessage(m.key.remoteJid, { text: "🔄 Reiniciando o bot..." });
      process.exit(0);
   return;
    } 

     if (/^subdono([1-9]|10)$/.test(command)) {
      if (numeroSender !== dono) return;

      const index = command === "subdono10" ? 9 : parseInt(command.replace("subdono", "")) - 1;
      while (subdonos.length < 10) subdonos.push("");

      const subNumero = args[0];
      if (!subNumero) return await hello.sendMessage(m.key.remoteJid, { text: `Digite o número do subdono. Ex: ${prefix}${command} 554899990123` });

      subdonos[index] = subNumero;
      dadosDonos.subdonos = subdonos;
      salvarDonos(dadosDonos);

      await hello.sendMessage(m.key.remoteJid, { text: `✅ Subdono ${index + 1} definido para: ${subNumero}` });
 return;
    } 

      if (command === "subdonos") {
      while (subdonos.length < 10) subdonos.push("");
      const lista = subdonos.map((s, i) => `💖 Subdono ${i + 1}: ${s || "💌 vazio"}`).join("\n");
      const mensagem = `╔══════════════════╗\n║   🌸 Lista de Subdonos 🌸  ║\n╠══════════════════╣\n${lista}\n╚══════════════════╝`;
      await hello.sendMessage(m.key.remoteJid, { text: mensagem });
 return;
    } 

     if (command === "info") {
      await hello.sendMessage(m.key.remoteJid, { text: `*📊 Informações do Bot*\n\nNode.js: ${process.version}\nBaileys: v${version.join(".")}` });
 return;
   } 

       if (command === "menu") {
      const menuText = gerarMenu({ dono, comandos, chatName, prefix });
      const caminhoImagem = "./menu.jpg";
      if (!fs.existsSync(caminhoImagem)) return await hello.sendMessage(m.key.remoteJid, { text: "❌ Imagem do menu não encontrada." });
      await hello.sendMessage(m.key.remoteJid, { image: { url: caminhoImagem }, caption: menuText });
 return;
    } 

      if (command === "prefixo") {
      if (!ehDonoOuSub) return await hello.sendMessage(m.key.remoteJid, { text: "❌ Apenas a dona ou os subdonos podem alterar o prefixo." });
      const novoPrefix = args[0];
      if (!novoPrefix) return await hello.sendMessage(m.key.remoteJid, { text: `Digite o novo prefixo. Ex: ${prefix}prefixo $` });
      prefix = novoPrefix;
      config.prefix = novoPrefix;
      salvarConfig(config);
      await hello.sendMessage(m.key.remoteJid, { text: `✅ Prefixo alterado para: ${novoPrefix}` });
 return;
    } 

    // --- Play áudio ---
       if (command === "play") {
      const query = args.join(" ");
      if (!query) return await hello.sendMessage(m.key.remoteJid, { text: `💖 Digite o nome ou link da música.\nEx: ${prefix}play despacito` });

      try {
        const resultado = await yts(query);
        const video = resultado.videos[0];
        if (!video) return await hello.sendMessage(m.key.remoteJid, { text: "❌ Nenhum vídeo encontrado." });

        const musicFolder = path.join(__dirname, "musicas");
        if (!fs.existsSync(musicFolder)) fs.mkdirSync(musicFolder);

        const safeTitle = video.title.replace(/[<>:"/\\|?*]/g, "");
        const audioPath = path.join(musicFolder, `${safeTitle}.mp3`);

        await hello.sendMessage(m.key.remoteJid, { text: `🌸💖 Música encontrada! 💖🌸\n\n🎵 *Título:* ${video.title}\n🔗 *Link:* ${video.url}\nBaixando e preparando para você...` });

        exec(`yt-dlp -x --audio-format mp3 -o "${audioPath}" "${video.url}"`, async (err) => {
          if (err) return await hello.sendMessage(m.key.remoteJid, { text: `❌ Erro ao baixar: ${err}` });

          await hello.sendMessage(m.key.remoteJid, {
            image: { url: video.thumbnail },
            caption: `🌸💖 Música pronta! 💖🌸\n\n🎵 *Título:* ${video.title}\n🔗 *Link:* ${video.url}`
          });

          await hello.sendMessage(m.key.remoteJid, {
            audio: fs.readFileSync(audioPath),
            mimetype: "audio/mpeg",
            ptt: false
          });

          fs.unlinkSync(audioPath);
        });

      } catch (e) {
        console.error(e);
        await hello.sendMessage(m.key.remoteJid, { text: `❌ Ocorreu um erro ao processar sua solicitação.` });
      }
return;
    } 

    // --- Play vídeo ---
        if (command === "playvid") {
      const query = args.join(" ");
      if (!query) return await hello.sendMessage(m.key.remoteJid, { text: `Digite o nome ou link do vídeo. Ex: ${prefix}playvid despacito` });
      try {
        const resultado = await yts(query);
        const video = resultado.videos[0];
        if (!video) return await hello.sendMessage(m.key.remoteJid, { text: "❌ Nenhum vídeo encontrado." });

        const videoFolder = path.join(__dirname, "videos");
        if (!fs.existsSync(videoFolder)) fs.mkdirSync(videoFolder);

        const safeTitle = video.title.replace(/[<>:"/\\|?*]/g, "");
        const videoPath = path.join(videoFolder, `${safeTitle}.mp4`);

        await hello.sendMessage(m.key.remoteJid, { text: `🌸💖 Vídeo encontrado! 💖🌸\n\n🎬 *Título:* ${video.title}\n🔗 *Link:* ${video.url}\nBaixando para você...` });

        exec(`yt-dlp -f mp4 -o "${videoPath}" "${video.url}"`, async (err) => {
          if (err) return await hello.sendMessage(m.key.remoteJid, { text: `❌ Erro ao baixar: ${err}` });

          await hello.sendMessage(m.key.remoteJid, {
            video: fs.readFileSync(videoPath),
            mimetype: "video/mp4"
          });

          fs.unlinkSync(videoPath);
        });

      } catch (e) {
        console.error(e);
        await hello.sendMessage(m.key.remoteJid, { text: `❌ Ocorreu um erro ao processar sua solicitação.` });
      }
return;
    } 

    // --- Comando 8D ---
        if (command === "8d") {
      let audioMessage;
      if (m.message.audioMessage) audioMessage = m.message.audioMessage;
      else if (m.message.extendedTextMessage?.contextInfo?.quotedMessage?.audioMessage) audioMessage = m.message.extendedTextMessage.contextInfo.quotedMessage.audioMessage;
      else return await hello.sendMessage(m.key.remoteJid, { text: `💖 Envie ou responda um áudio para aplicar o efeito 8D.\nEx: ${prefix}8D` });

      const tempFolder = path.join(__dirname, "temp");
      if (!fs.existsSync(tempFolder)) fs.mkdirSync(tempFolder);

      const inputFile = path.join(tempFolder, `input_${Date.now()}.mp3`);
      const outputFile = path.join(tempFolder, `output_8d_${Date.now()}.mp3`);

      try {
        const buffer = await downloadMediaMessage({ message: { audioMessage } }, "buffer", { logger: pino({ level: "silent" }) });
        fs.writeFileSync(inputFile, buffer);

        await hello.sendMessage(m.key.remoteJid, { text: "🌸💖 Aplicando efeito 8D ao seu áudio, aguarde... 💖🌸" });

        const ffmpegCmd = `ffmpeg -i ${inputFile} -filter_complex "\
apulsator=hz=0.4:width=0.8,\
aecho=0.8:0.9:40:0.3,\
dynaudnorm=g=7,\
acompressor=threshold=-20dB:ratio=2:attack=150:release=500,\
highpass=f=80,\
lowpass=f=15000" \
-c:a libmp3lame -q:a 2 ${outputFile}`;

        exec(ffmpegCmd, async (err) => {
          if (err) {
            console.error(err);
            return await hello.sendMessage(m.key.remoteJid, { text: `❌ Erro ao processar áudio.` });
          }

          await hello.sendMessage(m.key.remoteJid, {
            audio: fs.readFileSync(outputFile),
            mimetype: "audio/mpeg",
            ptt: false,
            caption: "🌸💖 Aqui está seu áudio com efeito 8D! 💖🌸"
          });

          fs.unlinkSync(inputFile);
          fs.unlinkSync(outputFile);
        });

      } catch (e) {
        console.error(e);
        await hello.sendMessage(m.key.remoteJid, { text: "❌ Ocorreu um erro ao processar seu áudio." });
      }
return;
    } 

if (command === "8dvideo") {
  let videoMessage;
  if (m.message.videoMessage) videoMessage = m.message.videoMessage;
  else if (m.message.extendedTextMessage?.contextInfo?.quotedMessage?.videoMessage) videoMessage = m.message.extendedTextMessage.contextInfo.quotedMessage.videoMessage;
  else return await hello.sendMessage(m.key.remoteJid, { text: `🎬💖 Envie ou responda um vídeo para aplicar o efeito 8D.\nEx: ${prefix}8dvideo` });

  const tempFolder = path.join(__dirname, "temp");
  if (!fs.existsSync(tempFolder)) fs.mkdirSync(tempFolder);

  const inputVideo = path.join(tempFolder, `input_${Date.now()}.mp4`);
  const extractedAudio = path.join(tempFolder, `extracted_${Date.now()}.m4a`);
  const audio8D = path.join(tempFolder, `8d_${Date.now()}.m4a`);
  const finalVideo = path.join(tempFolder, `final_${Date.now()}.mp4`);

  try {
    const buffer = await downloadMediaMessage({ message: { videoMessage } }, "buffer", { logger: pino({ level: "silent" }) });
    fs.writeFileSync(inputVideo, buffer);

    await hello.sendMessage(m.key.remoteJid, { text: "🌸💖 Aplicando efeito 8D ao seu vídeo, aguarde... 💖🌸" });

    // 1 - Extrair áudio do vídeo
    exec(`ffmpeg -i "${inputVideo}" -vn -acodec copy "${extractedAudio}"`, (err1) => {
      if (err1) {
        console.error(err1);
        return hello.sendMessage(m.key.remoteJid, { text: "❌ Erro ao extrair o áudio do vídeo." });
      }

      // 2 - Aplicar efeito 8D
      exec(`ffmpeg -i "${extractedAudio}" -filter_complex "apulsator=hz=0.4:width=0.8,aecho=0.8:0.9:40:0.3,dynaudnorm=g=7,acompressor=threshold=-20dB:ratio=2:attack=150:release=500,highpass=f=80,lowpass=f=15000" -y "${audio8D}"`, (err2) => {
        if (err2) {
          console.error(err2);
          return hello.sendMessage(m.key.remoteJid, { text: "❌ Erro ao aplicar efeito 8D no áudio." });
        }

        // 3 - Recompor vídeo com áudio novo
        exec(`ffmpeg -i "${inputVideo}" -i "${audio8D}" -map 0:v -map 1:a -c:v copy -c:a aac -shortest -y "${finalVideo}"`, async (err3) => {
          if (err3) {
            console.error(err3);
            return hello.sendMessage(m.key.remoteJid, { text: "❌ Erro ao recompor o vídeo." });
          }

          try {
            await hello.sendMessage(m.key.remoteJid, {
              video: fs.readFileSync(finalVideo),
              mimetype: "video/mp4",
              caption: "🎧🌸 Aqui está seu vídeo com efeito 8D aplicado! 💖🎶"
            }, { quoted: m });
          } catch (e) {
            console.error(e);
            await hello.sendMessage(m.key.remoteJid, { text: "❌ Erro ao enviar o vídeo." });
          } finally {
            // limpar arquivos
            [inputVideo, extractedAudio, audio8D, finalVideo].forEach(file => {
              if (fs.existsSync(file)) fs.unlinkSync(file);
            });
          }
        });
      });
    });

  } catch (e) {
    console.error(e);
    await hello.sendMessage(m.key.remoteJid, { text: "❌ Ocorreu um erro ao processar seu vídeo." });
  }
  return;
}

// --- Comandos de ADM de Grupo ---
if (["ban","promover","rebaixar","mute","desmute","d","reviver"].includes(command)) {
  const chatId = m.key.remoteJid;
  const userId = numeroSender + "@s.whatsapp.net";
  const perms = await checkGroupPermissions(chatId, userId);

  if (!perms.isGroup) return await hello.sendMessage(chatId, { text: "❌ Este comando só funciona em grupos." });
  if (!perms.isUserAdmin || !perms.isBotAdmin) return await hello.sendMessage(chatId, { text: "❌ Tanto você quanto o bot precisam ser admins." });

  // Cria ou carrega arquivo de mutados
  const mutedFile = "./muted.json";
  if (!fs.existsSync(mutedFile)) fs.writeFileSync(mutedFile, JSON.stringify({}));
  const mutedData = JSON.parse(fs.readFileSync(mutedFile));

  // Pega o target: pelo argumento ou pelo reply
  let target;
  if (m.message?.extendedTextMessage?.contextInfo?.participant) {
    target = m.message.extendedTextMessage.contextInfo.participant;
  } else if (args[0]) {
    target = args[0].replace(/\D/g, "") + "@s.whatsapp.net";
  }

  if (!target && !["desmute","reviver"].includes(command))
    return await hello.sendMessage(chatId, { text: `❌ Digite ou responda a mensagem do usuário.` });

  try {
    if (command === "ban") {
      await hello.groupParticipantsUpdate(chatId, [target], "remove");
      return;
    } 

    if (command === "promover") {
      await hello.groupParticipantsUpdate(chatId, [target], "promote");
      return;      
    } 

    if (command === "rebaixar") {
      await hello.groupParticipantsUpdate(chatId, [target], "demote");
      return;
    } 

    if (command === "reviver") {
      if (!target) return await hello.sendMessage(chatId, { text: "❌ Digite ou responda a mensagem do usuário para reviver." });

      try {
        await hello.groupParticipantsUpdate(chatId, [target], "add");
      } catch (err) {
        console.log("Erro ao adicionar direto:", err?.data || err);
        // Tenta enviar link de convite
        try {
          const inviteCode = await hello.groupInviteCode(chatId);
          const groupMetadata = await hello.groupMetadata(chatId);
          const inviteLink = `https://chat.whatsapp.com/${inviteCode}`;
          await hello.sendMessage(target, { 
            text: `Você foi convidado(a) para voltar ao grupo *${groupMetadata.subject}*.\nClique no link para entrar:\n${inviteLink}` 
          });
          await hello.sendMessage(chatId, { text: `📩 Convite enviado para ${target.split("@")[0]}` });
        } catch (e) {
          console.error("Falha ao enviar convite:", e);
          await hello.sendMessage(chatId, { text: "❌ Não foi possível adicionar nem enviar convite." });
        }
      }
      return;
    }

    if (command === "mute") {
      if (!mutedData[chatId]) mutedData[chatId] = [];
      if (!mutedData[chatId].includes(target)) mutedData[chatId].push(target);
      fs.writeFileSync(mutedFile, JSON.stringify(mutedData, null, 2));
      await hello.sendMessage(chatId, { text: `🔇 Usuário @${target.split("@")[0]} mutado com sucesso.`, mentions: [target] });
      return;
    } 

    if (command === "desmute") {
      if (mutedData[chatId]) {
        mutedData[chatId] = mutedData[chatId].filter(u => u !== target);
        fs.writeFileSync(mutedFile, JSON.stringify(mutedData, null, 2));
      }
      await hello.sendMessage(chatId, { text: `🔊 Usuário @${target.split("@")[0]} desmutado.`, mentions: [target] });
      return;
    } 

    if (command === "d") {
      let msgId = m.key.id;
      let participant = m.key.participant;
      if (m.message?.extendedTextMessage?.contextInfo) {
        const info = m.message.extendedTextMessage.contextInfo;
        msgId = info.stanzaId || msgId;
        participant = info.participant || participant;
      }
      await hello.sendMessage(chatId, { 
        delete: { remoteJid: chatId, fromMe: false, id: msgId, participant } 
      }).catch(()=>{});
      return;
    } 

    if (!["mute","desmute","d"].includes(command))
      await hello.sendMessage(chatId, { text: `✅ Comando ${command} executado com sucesso no usuário: ${target.split("@")[0]}` });

  } catch(e) {
    console.error(e);
    await hello.sendMessage(chatId, { text: `❌ Falha ao executar ${command}.` });
  }

} 

// --- Apagar mensagens de usuários mutados ---
hello.ev.on("messages.upsert", async ({ messages }) => {
  const m = messages[0];
  if (!m?.message) return;

  const chatId = m.key.remoteJid;
  if (!chatId.endsWith("@g.us")) return; // só grupos

  const senderId = m.key.participant || m.participant || m.key.remoteJid;
  const mutedFile = "./muted.json";
  if (!fs.existsSync(mutedFile)) return;

  const mutedData = JSON.parse(fs.readFileSync(mutedFile));
  if (!mutedData[chatId]?.includes(senderId)) return;

  try {
    // mesma lógica do comando "d"
    let msgId = m.key.id;
    let participant = m.key.participant || senderId;
    if (m.message?.extendedTextMessage?.contextInfo) {
      const info = m.message.extendedTextMessage.contextInfo;
      msgId = info.stanzaId || msgId;
      participant = info.participant || participant;
    }
    await hello.sendMessage(chatId, { 
      delete: { remoteJid: chatId, fromMe: false, id: msgId, participant } 
    }).catch(()=>{});
  } catch (err) {
    console.error("Erro ao apagar mensagem de mutado:", err);
  }
});

if (command === "infogp") {
  const chatId = m.key.remoteJid;
  const isGroup = chatId.endsWith("@g.us");
  if (!isGroup) return await hello.sendMessage(chatId, { text: "❌ Este comando só funciona em grupos, meu bem ✨" });

  try {
    // Metadados do grupo
    const groupMetadata = await hello.groupMetadata(chatId);
    const groupName = groupMetadata.subject;
    const groupDesc = groupMetadata.desc || "💖 Sem descrição fofinha...";
    const groupOwner = groupMetadata.owner || "🌸 Desconhecido";
    const creation = new Date(groupMetadata.creation * 1000).toLocaleDateString("pt-BR", {
      weekday: "long", year: "numeric", month: "long", day: "numeric"
    });
    const participants = groupMetadata.participants;

    // Filtra admins
    const admins = participants.filter(p => p.admin === "admin" || p.admin === "superadmin");
    const totalAdmins = admins.length;
    const totalMembros = participants.length;

    // Ativações do grupo
    const settings = [];
    if (groupMetadata.restrict) settings.push("🔒 Apenas admins podem mudar infos");
    else settings.push("🌸 Todos podem mudar infos fofinhas");
    if (groupMetadata.announcement) settings.push("📢 Apenas admins podem mandar mensagens");
    else settings.push("💌 Todos podem mandar mensagens doces");

    // Menções apenas de admins
    let adminMentions = admins.map(a => `💖 @${a.id.split("@")[0]}`).join("\n");

    const infoText = `
🎀 *💗 Nome do Grupo:* ${groupName}
👑 *🌸 Dona/Dono:* @${groupOwner.split("@")[0]}
🕒 *✨ Criado em:* ${creation}
👥 *💖 Total de membros:* ${totalMembros}
🛡️ *🌸 Total de admins:* ${totalAdmins}
📝 *💌 Descrição:* ${groupDesc}
⚙️ *✨ Ativações:* 
${settings.join("\n")}

💼 *👑 Admins fofinhos:*
${adminMentions}
    `;

    // Pega foto do grupo
    let ppUrl = null;
    try {
      ppUrl = await hello.profilePictureUrl(chatId, "image");
    } catch {
      ppUrl = null;
    }

    // Envia mensagem com menções
    const mentions = [groupOwner, ...admins.map(a => a.id)];
    if (ppUrl) {
      await hello.sendMessage(chatId, {
        image: { url: ppUrl },
        caption: infoText,
        mentions
      });
    } else {
      await hello.sendMessage(chatId, { text: infoText, mentions });
    }

  } catch (e) {
    console.error(e);
    await hello.sendMessage(chatId, { text: "❌ Oooops! Não consegui pegar as infos do grupo 😿" });
  }
return;
}

// --- Comando inválido ---
    const sugestao = sugerirComando(command, comandos);
    if (sugestao) {
      // Só sugere comando se o comando realmente não for encontrado
      await hello.sendMessage(m.key.remoteJid, { text: `❌ Comando não encontrado. Você quis dizer: *${sugestao}* ?` });
    } else if (!comandos.includes(command)) {
      // Só envia a mensagem de erro se o comando não estiver na lista de comandos
      await hello.sendMessage(m.key.remoteJid, { text: `❌ Comando não encontrado.` });
    }
  }); // fecha o messages.upsert
}     // fecha a função startBot

startBot();